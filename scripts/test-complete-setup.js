#!/usr/bin/env node

/**
 * Complete test script to verify all console errors are fixed
 * and the application is running error-free locally
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔧 Complete Setup Test - Verifying All Issues Are Fixed\n');

// Test 1: Check Supabase Status
console.log('1. ✅ Checking Supabase Status...');
try {
  const supabaseStatus = execSync('cd backend && npx supabase status', { 
    encoding: 'utf8',
    timeout: 10000 
  });
  
  if (supabaseStatus.includes('Started supabase local development setup')) {
    console.log('   ✅ Supabase is running locally');
    
    // Extract key information
    const apiUrlMatch = supabaseStatus.match(/API URL: (.+)/);
    const studioUrlMatch = supabaseStatus.match(/Studio URL: (.+)/);
    
    if (apiUrlMatch) console.log('   📍 API URL:', apiUrlMatch[1]);
    if (studioUrlMatch) console.log('   📍 Studio URL:', studioUrlMatch[1]);
  } else {
    console.log('   ❌ Supabase not running properly');
  }
} catch (error) {
  console.log('   ❌ Error checking Supabase:', error.message);
}

// Test 2: Check Environment Configuration
console.log('\n2. ✅ Checking Environment Configuration...');

const frontendEnvPath = path.join(__dirname, '../frontend/.env');
const backendEnvPath = path.join(__dirname, '../backend/.env');

try {
  const frontendEnv = fs.readFileSync(frontendEnvPath, 'utf8');
  const backendEnv = fs.readFileSync(backendEnvPath, 'utf8');
  
  const frontendUrl = frontendEnv.match(/VITE_SUPABASE_URL=(.+)/)?.[1];
  const backendUrl = backendEnv.match(/SUPABASE_URL=(.+)/)?.[1];
  
  if (frontendUrl === backendUrl && frontendUrl?.includes('127.0.0.1:54321')) {
    console.log('   ✅ Environment URLs match and point to local Supabase');
    console.log('   📍 URL:', frontendUrl);
  } else {
    console.log('   ❌ Environment mismatch:');
    console.log('      Frontend:', frontendUrl);
    console.log('      Backend:', backendUrl);
  }
} catch (error) {
  console.log('   ❌ Error reading environment files:', error.message);
}

// Test 3: Check Anonymous Sign-ins Configuration
console.log('\n3. ✅ Checking Anonymous Sign-ins Configuration...');

const configPath = path.join(__dirname, '../backend/supabase/config.toml');
try {
  const config = fs.readFileSync(configPath, 'utf8');
  const anonymousMatch = config.match(/enable_anonymous_sign_ins = (.+)/);
  
  if (anonymousMatch && anonymousMatch[1].trim() === 'true') {
    console.log('   ✅ Anonymous sign-ins are enabled');
  } else {
    console.log('   ❌ Anonymous sign-ins are disabled');
  }
} catch (error) {
  console.log('   ❌ Error reading config:', error.message);
}

// Test 4: Check Frontend Status
console.log('\n4. ✅ Checking Frontend Status...');

try {
  const response = execSync('curl -s -o /dev/null -w "%{http_code}" http://localhost:3000', { 
    encoding: 'utf8',
    timeout: 5000 
  });
  
  if (response.trim() === '200') {
    console.log('   ✅ Frontend is running at http://localhost:3000');
  } else {
    console.log('   ❌ Frontend not responding (HTTP', response.trim() + ')');
  }
} catch (error) {
  console.log('   ❌ Frontend not accessible');
}

// Test 5: Check Error Fix Implementation
console.log('\n5. ✅ Checking Error Fix Implementation...');

const errorTestUtilsPath = path.join(__dirname, '../frontend/src/utils/errorTestUtils.ts');
const authStorePath = path.join(__dirname, '../frontend/src/stores/authStore.ts');
const networkTestPath = path.join(__dirname, '../frontend/src/utils/networkTest.ts');

let fixesImplemented = 0;

if (fs.existsSync(errorTestUtilsPath)) {
  console.log('   ✅ Error test utilities implemented');
  fixesImplemented++;
} else {
  console.log('   ❌ Error test utilities missing');
}

if (fs.existsSync(authStorePath)) {
  const authStore = fs.readFileSync(authStorePath, 'utf8');
  if (authStore.includes('testConnection') && authStore.includes('session !== null')) {
    console.log('   ✅ Auth store connection test fixed');
    fixesImplemented++;
  } else {
    console.log('   ❌ Auth store connection test not fixed');
  }
} else {
  console.log('   ❌ Auth store file missing');
}

if (fs.existsSync(networkTestPath)) {
  const networkTest = fs.readFileSync(networkTestPath, 'utf8');
  if (networkTest.includes('error handling') || networkTest.includes('gracefully')) {
    console.log('   ✅ Network test error handling improved');
    fixesImplemented++;
  } else {
    console.log('   ❌ Network test error handling not improved');
  }
} else {
  console.log('   ❌ Network test file missing');
}

// Test 6: Test Anonymous Sign-in API Call
console.log('\n6. ✅ Testing Anonymous Sign-in API...');

try {
  const testResponse = execSync(`curl -s -X POST http://127.0.0.1:54321/auth/v1/signup \\
    -H "apikey: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0" \\
    -H "Content-Type: application/json" \\
    -d '{}'`, { 
    encoding: 'utf8',
    timeout: 5000 
  });
  
  if (testResponse.includes('access_token') || testResponse.includes('user')) {
    console.log('   ✅ Anonymous sign-in API is working');
  } else if (testResponse.includes('Anonymous sign-ins are disabled')) {
    console.log('   ❌ Anonymous sign-ins still disabled');
  } else {
    console.log('   ⚠️  API response unclear:', testResponse.substring(0, 100));
  }
} catch (error) {
  console.log('   ❌ Error testing API:', error.message);
}

// Summary
console.log('\n📊 Summary:');
console.log('   🎯 Original Issues:');
console.log('      1. Anonymous sign-ins error - Should be FIXED');
console.log('      2. Supabase connection null error - Should be FIXED');
console.log('      3. Edge function error - Should be FIXED');

console.log('\n   ✅ Fixes Implemented:', fixesImplemented, '/ 3');

console.log('\n💡 Next Steps:');
console.log('   1. Open http://localhost:3000 in your browser');
console.log('   2. Open browser console (F12)');
console.log('   3. Look for verification test results');
console.log('   4. Verify no red errors appear');
console.log('   5. Test anonymous sign-in functionality');

console.log('\n🎉 Setup verification complete!');
console.log('   If all tests pass, your application should be running error-free!');
