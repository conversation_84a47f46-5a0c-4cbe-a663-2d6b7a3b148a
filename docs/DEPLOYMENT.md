# AquaBell Deployment Guide

This guide covers deploying AquaBell to production environments.

## Prerequisites

- Completed [SETUP.md](./SETUP.md) for local development
- Vercel account (for frontend)
- Supabase project (for backend)
- Firebase project (for notifications)
- Domain name (optional)

## Environment Setup

### Production Environment Variables

Create production environment files:

**Frontend (.env.production)**:
```env
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your_production_anon_key
VITE_FIREBASE_API_KEY=your_production_firebase_api_key
VITE_FIREBASE_AUTH_DOMAIN=your-project-id.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your-project-id
VITE_FIREBASE_STORAGE_BUCKET=your-project-id.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
VITE_FIREBASE_APP_ID=your_app_id
VITE_FIREBASE_VAPID_KEY=your_production_vapid_key
VITE_NODE_ENV=production
```

**Backend (Supabase Edge Functions)**:
```env
FIREBASE_SERVER_KEY=your_production_server_key
FIREBASE_PROJECT_ID=your-project-id
```

## Frontend Deployment (Vercel)

### 1. Prepare for Deployment

```bash
cd frontend
npm run build
```

### 2. Deploy to Vercel

#### Option A: Vercel CLI
```bash
npm install -g vercel
vercel login
vercel --prod
```

#### Option B: GitHub Integration
1. Push code to GitHub
2. Connect repository to Vercel
3. Configure environment variables in Vercel dashboard
4. Deploy automatically on push

### 3. Configure Vercel

**vercel.json** (optional):
```json
{
  "buildCommand": "npm run build",
  "outputDirectory": "dist",
  "framework": "vite",
  "rewrites": [
    {
      "source": "/(.*)",
      "destination": "/index.html"
    }
  ],
  "headers": [
    {
      "source": "/sw.js",
      "headers": [
        {
          "key": "Cache-Control",
          "value": "no-cache, no-store, must-revalidate"
        }
      ]
    },
    {
      "source": "/firebase-messaging-sw.js",
      "headers": [
        {
          "key": "Cache-Control",
          "value": "no-cache, no-store, must-revalidate"
        }
      ]
    }
  ]
}
```

### 4. Environment Variables in Vercel

Add all `VITE_*` variables in:
- Vercel Dashboard → Project → Settings → Environment Variables

## Backend Deployment (Supabase)

### 1. Deploy Database Migrations

```bash
cd backend
npx supabase db push --linked
```

### 2. Deploy Edge Functions

```bash
npx supabase functions deploy send-reminders
```

### 3. Set Environment Variables

In Supabase Dashboard:
1. Go to Edge Functions
2. Select `send-reminders` function
3. Add environment variables:
   - `FIREBASE_SERVER_KEY`
   - `FIREBASE_PROJECT_ID`

### 4. Configure Cron Job

#### Option A: Supabase Cron (Recommended)
```sql
-- Run in Supabase SQL Editor
SELECT cron.schedule(
  'send-hydration-reminders',
  '0 * * * *', -- Every hour
  $$
  SELECT net.http_post(
    url := 'https://your-project-id.supabase.co/functions/v1/send-reminders',
    headers := '{"Authorization": "Bearer ' || current_setting('app.settings.service_role_key') || '", "Content-Type": "application/json"}'::jsonb
  );
  $$
);
```

#### Option B: External Cron Service
Use services like:
- GitHub Actions
- Vercel Cron Jobs
- Uptime Robot
- Cron-job.org

Example GitHub Action (`.github/workflows/cron.yml`):
```yaml
name: Send Hydration Reminders
on:
  schedule:
    - cron: '0 * * * *' # Every hour
jobs:
  send-reminders:
    runs-on: ubuntu-latest
    steps:
      - name: Call Edge Function
        run: |
          curl -X POST \
            -H "Authorization: Bearer ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}" \
            -H "Content-Type: application/json" \
            https://your-project-id.supabase.co/functions/v1/send-reminders
```

## Firebase Configuration

### 1. Update Service Worker

Update `frontend/public/firebase-messaging-sw.js` with production config:

```javascript
const firebaseConfig = {
  apiKey: "your_production_api_key",
  authDomain: "your-project-id.firebaseapp.com",
  projectId: "your-project-id",
  storageBucket: "your-project-id.appspot.com",
  messagingSenderId: "your_sender_id",
  appId: "your_app_id"
}
```

### 2. Configure Authorized Domains

In Firebase Console:
1. Go to Authentication → Settings → Authorized domains
2. Add your production domain (e.g., `aquabell.vercel.app`)

### 3. Update FCM Settings

1. Go to Cloud Messaging → Web configuration
2. Add your production domain to authorized origins

## Domain Configuration (Optional)

### 1. Custom Domain on Vercel

1. Go to Vercel Dashboard → Project → Settings → Domains
2. Add your custom domain
3. Configure DNS records as instructed

### 2. Update Firebase Authorized Domains

Add your custom domain to Firebase authorized domains.

### 3. Update Environment Variables

Update all environment variables to use your custom domain.

## SSL/HTTPS

Both Vercel and Supabase provide automatic HTTPS. Ensure:
- All API calls use HTTPS
- Service workers require HTTPS
- Firebase messaging requires HTTPS

## Performance Optimization

### 1. Frontend Optimizations

```bash
# Analyze bundle size
npm run build
npx vite-bundle-analyzer dist

# Optimize images
# Use WebP format for icons
# Compress images
```

### 2. Caching Strategy

Configure appropriate cache headers:
- Static assets: Long-term caching
- Service worker: No caching
- API responses: Short-term caching

### 3. Database Optimization

- Enable connection pooling in Supabase
- Add appropriate indexes
- Monitor query performance

## Monitoring and Logging

### 1. Frontend Monitoring

- Vercel Analytics
- Google Analytics (optional)
- Error tracking (Sentry, LogRocket)

### 2. Backend Monitoring

- Supabase Dashboard metrics
- Edge Function logs
- Database performance metrics

### 3. Notification Monitoring

- FCM delivery reports
- Notification engagement metrics
- Error rate monitoring

## Security Checklist

- [ ] Environment variables are secure
- [ ] API keys are not exposed in frontend
- [ ] RLS policies are properly configured
- [ ] CORS settings are restrictive
- [ ] HTTPS is enforced everywhere
- [ ] Service role key is protected
- [ ] Firebase security rules are configured

## Backup and Recovery

### 1. Database Backups

Supabase provides automatic backups. For additional safety:
- Export schema regularly
- Backup critical data
- Test restore procedures

### 2. Code Backups

- Use Git for version control
- Tag releases
- Maintain deployment documentation

## Troubleshooting

### Common Issues

1. **Notifications not working**:
   - Check VAPID key configuration
   - Verify service worker registration
   - Check Firebase project settings

2. **CORS errors**:
   - Update Supabase allowed origins
   - Check API endpoint URLs

3. **Build failures**:
   - Check environment variables
   - Verify dependencies
   - Check TypeScript errors

4. **Cron job not running**:
   - Verify cron syntax
   - Check function logs
   - Test function manually

### Logs and Debugging

- Frontend: Browser console, Vercel function logs
- Backend: Supabase Edge Function logs
- Database: Supabase dashboard logs
- Notifications: Firebase console

## Rollback Strategy

1. Keep previous deployment available
2. Use Vercel's instant rollback feature
3. Have database migration rollback scripts
4. Monitor metrics after deployment

## Post-Deployment Checklist

- [ ] Frontend loads correctly
- [ ] User registration works
- [ ] Notifications can be enabled
- [ ] Settings can be updated
- [ ] Cron job is running
- [ ] All environment variables are set
- [ ] SSL certificates are valid
- [ ] Performance is acceptable
- [ ] Error rates are low
