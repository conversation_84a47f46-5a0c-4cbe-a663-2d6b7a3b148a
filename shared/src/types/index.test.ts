import { 
  DEFAULT_PREFERENCES, 
  HYDRATION_REMINDERS,
  AquaBellError,
  ValidationError,
  NotFoundError,
  UnauthorizedError,
  NotificationType,
  DeliveryStatus
} from './index'

describe('Default Preferences', () => {
  test('should have valid default values', () => {
    expect(DEFAULT_PREFERENCES.daily_goal_ml).toBe(2000)
    expect(DEFAULT_PREFERENCES.wake_time).toBe('07:00')
    expect(DEFAULT_PREFERENCES.sleep_time).toBe('22:00')
    expect(DEFAULT_PREFERENCES.reminder_interval_minutes).toBe(60)
    expect(DEFAULT_PREFERENCES.is_notifications_enabled).toBe(true)
  })
})

describe('Hydration Reminders', () => {
  test('should have at least one reminder', () => {
    expect(HYDRATION_REMINDERS.length).toBeGreaterThan(0)
  })

  test('each reminder should have required properties', () => {
    HYDRATION_REMINDERS.forEach(reminder => {
      expect(reminder).toHaveProperty('title')
      expect(reminder).toHaveProperty('body')
      expect(reminder).toHaveProperty('emoji')
      expect(typeof reminder.title).toBe('string')
      expect(typeof reminder.body).toBe('string')
      expect(typeof reminder.emoji).toBe('string')
      expect(reminder.title.length).toBeGreaterThan(0)
      expect(reminder.body.length).toBeGreaterThan(0)
    })
  })
})

describe('Error Classes', () => {
  test('AquaBellError should extend Error', () => {
    const error = new AquaBellError('Test message', 'TEST_CODE', 400)
    expect(error).toBeInstanceOf(Error)
    expect(error.name).toBe('AquaBellError')
    expect(error.message).toBe('Test message')
    expect(error.code).toBe('TEST_CODE')
    expect(error.statusCode).toBe(400)
  })

  test('ValidationError should extend AquaBellError', () => {
    const error = new ValidationError('Invalid input')
    expect(error).toBeInstanceOf(AquaBellError)
    expect(error.name).toBe('ValidationError')
    expect(error.code).toBe('VALIDATION_ERROR')
    expect(error.statusCode).toBe(400)
  })

  test('NotFoundError should extend AquaBellError', () => {
    const error = new NotFoundError('User')
    expect(error).toBeInstanceOf(AquaBellError)
    expect(error.name).toBe('NotFoundError')
    expect(error.message).toBe('User not found')
    expect(error.code).toBe('NOT_FOUND')
    expect(error.statusCode).toBe(404)
  })

  test('UnauthorizedError should extend AquaBellError', () => {
    const error = new UnauthorizedError()
    expect(error).toBeInstanceOf(AquaBellError)
    expect(error.name).toBe('UnauthorizedError')
    expect(error.code).toBe('UNAUTHORIZED')
    expect(error.statusCode).toBe(401)
  })
})

describe('Enums', () => {
  test('NotificationType should have expected values', () => {
    expect(NotificationType.HYDRATION_REMINDER).toBe('hydration_reminder')
    expect(NotificationType.DAILY_GOAL_REMINDER).toBe('daily_goal_reminder')
    expect(NotificationType.WELCOME).toBe('welcome')
    expect(NotificationType.ACHIEVEMENT).toBe('achievement')
  })

  test('DeliveryStatus should have expected values', () => {
    expect(DeliveryStatus.PENDING).toBe('pending')
    expect(DeliveryStatus.SENT).toBe('sent')
    expect(DeliveryStatus.DELIVERED).toBe('delivered')
    expect(DeliveryStatus.FAILED).toBe('failed')
    expect(DeliveryStatus.RETRY).toBe('retry')
  })
})
