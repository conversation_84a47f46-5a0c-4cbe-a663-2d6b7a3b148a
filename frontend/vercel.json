{"buildCommand": "npm run build", "outputDirectory": "dist", "framework": "vite", "rewrites": [{"source": "/(.*)", "destination": "/index.html"}], "headers": [{"source": "/sw.js", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}, {"key": "Service-Worker-Allowed", "value": "/"}]}, {"source": "/firebase-messaging-sw.js", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}, {"key": "Service-Worker-Allowed", "value": "/"}]}, {"source": "/icons/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/manifest.json", "headers": [{"key": "Cache-Control", "value": "public, max-age=86400"}]}]}