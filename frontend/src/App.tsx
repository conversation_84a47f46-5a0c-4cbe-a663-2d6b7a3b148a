// Phase 3: Adding React Router and Components
import { useEffect, useState } from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { useAuthStore } from './stores/authStore'
import { useNotificationStore } from './stores/notificationStore'
import { initializeFirebase } from './services/firebase'
import Layout from './components/Layout'
import OnboardingPage from './pages/OnboardingPage'
import HomePage from './pages/HomePage'
import AchievementsPage from './pages/AchievementsPage'
import SettingsPage from './pages/SettingsPage'
import TestNotificationsPage from './pages/TestNotificationsPage'

function App() {
  console.log('🚀 App component is rendering!')
  
  const { user, initialize } = useAuthStore()
  const { initializeNotifications } = useNotificationStore()
  const [initComplete, setInitComplete] = useState(false)

  useEffect(() => {
    console.log('🔄 Starting app initialization...')

    // Initialize Firebase first
    initializeFirebase()

    // Initialize auth and notifications in parallel
    Promise.all([
      initialize(),
      initializeNotifications()
    ])
      .then(() => {
        console.log('✅ App initialization complete')
        setInitComplete(true)
      })
      .catch((error) => {
        console.error('❌ App initialization failed:', error)
        setInitComplete(true) // Still mark as complete to show the UI
      })
  }, [initialize, initializeNotifications])

  // Show loading state during initialization
  if (!initComplete) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-cyan-50 flex items-center justify-center">
        <div className="bg-white rounded-xl shadow-lg p-8 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Initializing AquaBell...</p>
        </div>
      </div>
    )
  }

  // Main app with routing
  return (
    <Router>
      <Routes>
        {/* Show onboarding if user is not authenticated */}
        {!user && (
          <Route path="*" element={<OnboardingPage />} />
        )}

        {/* Authenticated routes */}
        {user && (
          <Route path="/" element={<Layout />}>
            <Route index element={<HomePage />} />
            <Route path="achievements" element={<AchievementsPage />} />
            <Route path="settings" element={<SettingsPage />} />
            <Route path="test-notifications" element={<TestNotificationsPage />} />
            <Route path="*" element={<Navigate to="/" replace />} />
          </Route>
        )}
      </Routes>
    </Router>
  )
}

export default App
