// Comprehensive Notification Testing Page
import { useState, useEffect } from 'react'
import { useNotificationStore } from '../stores/notificationStore'
import { useAuthStore } from '../stores/authStore'
import {
  checkNotificationHealth,
  showTestNotification,
  formatNotificationPermission
} from '../utils/notifications'
import { getFCMToken } from '../services/firebase'
import { sendTestFCMNotification, runFCMTestSuite } from '../utils/fcmTesting'
import LoadingSpinner from '../components/LoadingSpinner'
import FCMTestingGuide from '../components/FCMTestingGuide'

interface HealthCheck {
  isSupported: boolean
  permission: NotificationPermission
  serviceWorkerRegistered: boolean
  fcmSupported: boolean
}

interface TestResult {
  type: string
  status: 'success' | 'error' | 'warning'
  message: string
  details?: any
}

export default function TestNotificationsPage() {
  const { permission, requestPermission, loading, fcmToken } = useNotificationStore()
  const { user } = useAuthStore()
  const [healthCheck, setHealthCheck] = useState<HealthCheck | null>(null)
  const [testResults, setTestResults] = useState<TestResult[]>([])
  const [isRunningTests, setIsRunningTests] = useState(false)
  const [currentFCMToken, setCurrentFCMToken] = useState<string | null>(null)

  useEffect(() => {
    runHealthCheck()
    loadFCMToken()
  }, [])

  const runHealthCheck = async () => {
    try {
      const health = await checkNotificationHealth()
      setHealthCheck(health)
    } catch (error) {
      console.error('Health check failed:', error)
    }
  }

  const loadFCMToken = async () => {
    try {
      const token = await getFCMToken()
      setCurrentFCMToken(token)
    } catch (error) {
      console.error('Failed to get FCM token:', error)
    }
  }

  const addTestResult = (result: TestResult) => {
    setTestResults(prev => [...prev, result])
  }

  const clearTestResults = () => {
    setTestResults([])
  }

  const testLocalNotification = async () => {
    try {
      const success = await showTestNotification()
      addTestResult({
        type: 'Local Notification',
        status: success ? 'success' : 'error',
        message: success ? 'Local notification sent successfully' : 'Failed to send local notification'
      })
    } catch (error) {
      addTestResult({
        type: 'Local Notification',
        status: 'error',
        message: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`
      })
    }
  }

  const testFCMNotification = async () => {
    if (!currentFCMToken) {
      addTestResult({
        type: 'FCM Notification',
        status: 'error',
        message: 'No FCM token available'
      })
      return
    }

    try {
      const result = await sendTestFCMNotification({
        fcmToken: currentFCMToken,
        title: 'AquaBell FCM Test',
        body: 'This is a test notification sent via Firebase Cloud Messaging! 💧',
        data: {
          test: 'true',
          timestamp: new Date().toISOString()
        }
      })

      addTestResult({
        type: 'FCM Notification',
        status: result.success ? 'success' : 'error',
        message: result.success
          ? 'FCM notification sent successfully'
          : `Failed to send FCM notification: ${result.error}`,
        details: result.details
      })
    } catch (error) {
      addTestResult({
        type: 'FCM Notification',
        status: 'error',
        message: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`
      })
    }
  }

  const runAllTests = async () => {
    setIsRunningTests(true)
    clearTestResults()

    // Test 1: Health Check
    await runHealthCheck()
    await new Promise(resolve => setTimeout(resolve, 500))

    // Test 2: Local Notification
    await testLocalNotification()
    await new Promise(resolve => setTimeout(resolve, 1000))

    // Test 3: FCM Token
    await loadFCMToken()
    await new Promise(resolve => setTimeout(resolve, 500))

    // Test 4: FCM Notification (if token available)
    if (currentFCMToken) {
      await testFCMNotification()
    }

    setIsRunningTests(false)
  }

  const permissionInfo = formatNotificationPermission(permission)

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="bg-white rounded-xl shadow-lg p-6">
        <h1 className="text-2xl font-bold text-gray-800 mb-2 text-center">
          🔔 Notification Testing Center
        </h1>
        <p className="text-gray-600 text-center">
          Comprehensive testing for Firebase Cloud Messaging and local notifications
        </p>
      </div>

      {/* Permission Status */}
      <div className="bg-white rounded-xl shadow-lg p-6">
        <h2 className="text-lg font-semibold text-gray-800 mb-4">Permission Status</h2>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <span className="text-2xl">{permissionInfo.icon}</span>
            <div>
              <p className="font-medium text-gray-800">Notifications</p>
              <p className={`text-sm ${permissionInfo.color}`}>
                {permissionInfo.text}
              </p>
            </div>
          </div>
          {permission !== 'granted' && (
            <button
              onClick={requestPermission}
              disabled={loading}
              className="bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center gap-2"
            >
              {loading && <LoadingSpinner size="small" />}
              {loading ? 'Requesting...' : 'Request Permission'}
            </button>
          )}
        </div>
      </div>

      {/* Health Check */}
      {healthCheck && (
        <div className="bg-white rounded-xl shadow-lg p-6">
          <h2 className="text-lg font-semibold text-gray-800 mb-4">System Health Check</h2>
          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <span className="text-sm font-medium">Browser Support</span>
              <span className={healthCheck.isSupported ? 'text-green-600' : 'text-red-600'}>
                {healthCheck.isSupported ? '✅' : '❌'}
              </span>
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <span className="text-sm font-medium">Service Worker</span>
              <span className={healthCheck.serviceWorkerRegistered ? 'text-green-600' : 'text-red-600'}>
                {healthCheck.serviceWorkerRegistered ? '✅' : '❌'}
              </span>
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <span className="text-sm font-medium">FCM Support</span>
              <span className={healthCheck.fcmSupported ? 'text-green-600' : 'text-red-600'}>
                {healthCheck.fcmSupported ? '✅' : '❌'}
              </span>
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <span className="text-sm font-medium">Permission</span>
              <span className={healthCheck.permission === 'granted' ? 'text-green-600' : 'text-yellow-600'}>
                {healthCheck.permission === 'granted' ? '✅' : '⚠️'}
              </span>
            </div>
          </div>
        </div>
      )}

      {/* FCM Token Info */}
      {currentFCMToken && (
        <div className="bg-white rounded-xl shadow-lg p-6">
          <h2 className="text-lg font-semibold text-gray-800 mb-4">FCM Token</h2>
          <div className="bg-gray-50 p-3 rounded-lg">
            <p className="text-xs font-mono text-gray-600 break-all">
              {currentFCMToken}
            </p>
          </div>
          <p className="text-sm text-gray-500 mt-2">
            This token is used to send push notifications to this device
          </p>
        </div>
      )}

      {/* Test Controls */}
      <div className="bg-white rounded-xl shadow-lg p-6">
        <h2 className="text-lg font-semibold text-gray-800 mb-4">Test Controls</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
          <button
            onClick={testLocalNotification}
            disabled={permission !== 'granted'}
            className="bg-green-500 hover:bg-green-600 disabled:bg-gray-400 text-white font-medium py-2 px-4 rounded-lg transition-colors"
          >
            Test Local Notification
          </button>
          <button
            onClick={testFCMNotification}
            disabled={!currentFCMToken || permission !== 'granted'}
            className="bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white font-medium py-2 px-4 rounded-lg transition-colors"
          >
            Test FCM Notification
          </button>
          <button
            onClick={runAllTests}
            disabled={isRunningTests}
            className="bg-purple-500 hover:bg-purple-600 disabled:bg-gray-400 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center gap-2"
          >
            {isRunningTests && <LoadingSpinner size="small" />}
            {isRunningTests ? 'Running Tests...' : 'Run All Tests'}
          </button>
        </div>
      </div>

      {/* FCM Testing Guide */}
      <FCMTestingGuide />

      {/* Test Results */}
      {testResults.length > 0 && (
        <div className="bg-white rounded-xl shadow-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-800">Test Results</h2>
            <button
              onClick={clearTestResults}
              className="text-sm text-gray-500 hover:text-gray-700"
            >
              Clear Results
            </button>
          </div>
          <div className="space-y-3">
            {testResults.map((result, index) => (
              <div
                key={index}
                className={`p-3 rounded-lg border-l-4 ${
                  result.status === 'success'
                    ? 'bg-green-50 border-green-500'
                    : result.status === 'error'
                    ? 'bg-red-50 border-red-500'
                    : 'bg-yellow-50 border-yellow-500'
                }`}
              >
                <div className="flex items-center justify-between">
                  <span className="font-medium text-gray-800">{result.type}</span>
                  <span className={
                    result.status === 'success'
                      ? 'text-green-600'
                      : result.status === 'error'
                      ? 'text-red-600'
                      : 'text-yellow-600'
                  }>
                    {result.status === 'success' ? '✅' : result.status === 'error' ? '❌' : '⚠️'}
                  </span>
                </div>
                <p className="text-sm text-gray-600 mt-1">{result.message}</p>
                {result.details && (
                  <details className="mt-2">
                    <summary className="text-xs text-gray-500 cursor-pointer">Show Details</summary>
                    <pre className="text-xs text-gray-600 mt-1 bg-gray-100 p-2 rounded overflow-auto">
                      {JSON.stringify(result.details, null, 2)}
                    </pre>
                  </details>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
