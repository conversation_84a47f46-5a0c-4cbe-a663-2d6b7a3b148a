import { useEffect, useState } from 'react'
import { usePreferencesStore } from '../stores/preferencesStore'
import { useNotificationStore } from '../stores/notificationStore'
import { useAuthStore } from '../stores/authStore'
import { formatVolume } from '../utils/format'
import LoadingSpinner from '../components/LoadingSpinner'
import NotificationTestCard from '../components/NotificationTestCard'

export default function SettingsPage() {
  const { preferences, updatePreferences, resetToDefaults, loading } = usePreferencesStore()
  const { requestPermission, permission, isSupported } = useNotificationStore()
  const { signOut } = useAuthStore()
  
  const [formData, setFormData] = useState({
    daily_goal_ml: 2000,
    wake_time: '07:00',
    sleep_time: '22:00',
    reminder_interval_minutes: 60,
    is_notifications_enabled: true
  })

  useEffect(() => {
    if (preferences) {
      setFormData({
        daily_goal_ml: preferences.daily_goal_ml,
        wake_time: preferences.wake_time,
        sleep_time: preferences.sleep_time,
        reminder_interval_minutes: preferences.reminder_interval_minutes,
        is_notifications_enabled: preferences.is_notifications_enabled
      })
    }
  }, [preferences])

  const handleSave = async () => {
    await updatePreferences(formData)
  }

  const handleReset = async () => {
    await resetToDefaults()
  }

  const handleNotificationToggle = async () => {
    if (!formData.is_notifications_enabled) {
      // Enabling notifications
      if (permission !== 'granted') {
        const success = await requestPermission()
        if (success) {
          setFormData(prev => ({ ...prev, is_notifications_enabled: true }))
        }
      } else {
        setFormData(prev => ({ ...prev, is_notifications_enabled: true }))
      }
    } else {
      // Disabling notifications
      setFormData(prev => ({ ...prev, is_notifications_enabled: false }))
    }
  }

  const goalOptions = [
    { value: 1500, label: '1.5L - Light' },
    { value: 2000, label: '2L - Standard' },
    { value: 2500, label: '2.5L - Active' },
    { value: 3000, label: '3L - Athletic' },
    { value: 3500, label: '3.5L - Intense' }
  ]

  const intervalOptions = [
    { value: 30, label: '30 minutes' },
    { value: 60, label: '1 hour' },
    { value: 90, label: '1.5 hours' },
    { value: 120, label: '2 hours' }
  ]

  return (
    <div className="min-h-screen p-4">
      <div className="max-w-md mx-auto space-y-6">
        {/* Header */}
        <div className="text-center pt-8">
          <h1 className="text-3xl font-bold text-gradient mb-2">
            Settings
          </h1>
          <p className="text-gray-600">
            Customize your hydration preferences
          </p>
        </div>

        {/* Daily Goal */}
        <div className="card">
          <h2 className="text-lg font-semibold text-gray-800 mb-4">Daily Goal</h2>
          <div className="space-y-3">
            {goalOptions.map(option => (
              <label key={option.value} className="flex items-center gap-3 cursor-pointer">
                <input
                  type="radio"
                  name="daily_goal"
                  value={option.value}
                  checked={formData.daily_goal_ml === option.value}
                  onChange={(e) => setFormData(prev => ({ 
                    ...prev, 
                    daily_goal_ml: parseInt(e.target.value) 
                  }))}
                  className="w-4 h-4 text-primary-600"
                />
                <span className="flex-1">{option.label}</span>
                <span className="text-sm text-gray-500">
                  {formatVolume(option.value)}
                </span>
              </label>
            ))}
          </div>
        </div>

        {/* Active Hours */}
        <div className="card">
          <h2 className="text-lg font-semibold text-gray-800 mb-4">Active Hours</h2>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Wake Time
              </label>
              <input
                type="time"
                value={formData.wake_time}
                onChange={(e) => setFormData(prev => ({ 
                  ...prev, 
                  wake_time: e.target.value 
                }))}
                className="input-field"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Sleep Time
              </label>
              <input
                type="time"
                value={formData.sleep_time}
                onChange={(e) => setFormData(prev => ({ 
                  ...prev, 
                  sleep_time: e.target.value 
                }))}
                className="input-field"
              />
            </div>
          </div>
          <p className="text-sm text-gray-600 mt-2">
            You'll only receive reminders during these hours
          </p>
        </div>

        {/* Reminder Frequency */}
        <div className="card">
          <h2 className="text-lg font-semibold text-gray-800 mb-4">Reminder Frequency</h2>
          <div className="space-y-3">
            {intervalOptions.map(option => (
              <label key={option.value} className="flex items-center gap-3 cursor-pointer">
                <input
                  type="radio"
                  name="reminder_interval"
                  value={option.value}
                  checked={formData.reminder_interval_minutes === option.value}
                  onChange={(e) => setFormData(prev => ({ 
                    ...prev, 
                    reminder_interval_minutes: parseInt(e.target.value) 
                  }))}
                  className="w-4 h-4 text-primary-600"
                />
                <span>{option.label}</span>
              </label>
            ))}
          </div>
        </div>

        {/* Notifications */}
        <div className="card">
          <h2 className="text-lg font-semibold text-gray-800 mb-4">Notifications</h2>
          
          {!isSupported ? (
            <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-2xl">
              <p className="text-yellow-800 text-sm">
                Notifications are not supported on this device
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              <label className="flex items-center justify-between cursor-pointer">
                <div>
                  <span className="font-medium text-gray-800">Enable Reminders</span>
                  <p className="text-sm text-gray-600">
                    Get push notifications for hydration reminders
                  </p>
                </div>
                <input
                  type="checkbox"
                  checked={formData.is_notifications_enabled}
                  onChange={handleNotificationToggle}
                  className="w-5 h-5 text-primary-600 rounded"
                />
              </label>

              <div className="p-3 bg-gray-50 rounded-2xl">
                <p className="text-sm text-gray-600">
                  Status: <span className="font-medium">
                    {permission === 'granted' ? 'Enabled' : 
                     permission === 'denied' ? 'Blocked' : 'Not requested'}
                  </span>
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="space-y-3">
          <button
            onClick={handleSave}
            disabled={loading}
            className="btn-primary w-full flex items-center justify-center gap-2"
          >
            {loading && <LoadingSpinner size="small" />}
            Save Changes
          </button>

          <button
            onClick={handleReset}
            disabled={loading}
            className="btn-secondary w-full"
          >
            Reset to Defaults
          </button>
        </div>

        {/* Notification Testing (Development) */}
        {import.meta.env.DEV && (
          <NotificationTestCard />
        )}

        {/* Account Actions */}
        <div className="card">
          <h2 className="text-lg font-semibold text-gray-800 mb-4">Account</h2>
          <button
            onClick={signOut}
            className="w-full py-3 px-4 bg-red-50 hover:bg-red-100 text-red-600 font-medium rounded-2xl transition-colors duration-200"
          >
            Sign Out
          </button>
          <p className="text-xs text-gray-500 mt-2 text-center">
            Your data will be cleared from this device
          </p>
        </div>
      </div>
    </div>
  )
}
