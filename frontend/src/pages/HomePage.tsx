import { useEffect, useState } from 'react'
import { usePreferencesStore } from '../stores/preferencesStore'
import { useNotificationStore } from '../stores/notificationStore'
import { useWaterIntake } from '../hooks/useWaterIntake'
import { formatVolume } from '../utils/format'
import LoadingSpinner from '../components/LoadingSpinner'
import WaterProgressRing from '../components/WaterProgressRing'
import WaterIntakeHistory from '../components/WaterIntakeHistory'
import WeeklyChart from '../components/WeeklyChart'
import CustomAmountModal from '../components/CustomAmountModal'

export default function HomePage() {
  const { preferences, loadPreferences, loading } = usePreferencesStore()
  const { permission, isSupported } = useNotificationStore()
  const { currentIntake, progress, dailyGoal, addWaterIntake, getLastDrinkTime } = useWaterIntake()
  const [showCustomModal, setShowCustomModal] = useState(false)

  useEffect(() => {
    loadPreferences()
  }, [loadPreferences])

  const handleDrinkWater = (amount: number) => {
    addWaterIntake(amount, 'quick_add')
  }

  const handleCustomAmount = (amount: number) => {
    addWaterIntake(amount, 'manual')
  }

  const getMotivationalMessage = () => {
    if (progress === 0) return "Let's start your hydration journey! 🌟"
    if (progress < 25) return "Great start! Keep it up! 💪"
    if (progress < 50) return "You're doing amazing! 🎉"
    if (progress < 75) return "Almost there! You've got this! 🚀"
    if (progress < 100) return "So close to your goal! 🏆"
    return "Goal achieved! You're a hydration hero! 🦸‍♀️"
  }

  const getTimeUntilNextReminder = () => {
    const lastDrinkTime = getLastDrinkTime()
    if (!preferences || !lastDrinkTime) return null

    // Ensure lastDrinkTime is a Date object
    const lastDrinkDate = lastDrinkTime instanceof Date ? lastDrinkTime : new Date(lastDrinkTime)
    const nextReminder = new Date(lastDrinkDate.getTime() + preferences.reminder_interval_minutes * 60 * 1000)
    const now = new Date()
    const diff = nextReminder.getTime() - now.getTime()

    if (diff <= 0) return "Due now"

    const minutes = Math.floor(diff / (1000 * 60))
    const hours = Math.floor(minutes / 60)

    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`
    }
    return `${minutes}m`
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="large" />
      </div>
    )
  }

  return (
    <div className="min-h-screen p-4">
      <div className="max-w-md mx-auto space-y-6">
        {/* Header */}
        <div className="text-center pt-8">
          <h1 className="text-3xl font-bold text-gradient mb-2">
            AquaBell
          </h1>
          <p className="text-gray-600">
            {new Date().toLocaleDateString('en-US', { 
              weekday: 'long', 
              month: 'long', 
              day: 'numeric' 
            })}
          </p>
        </div>

        {/* Progress Ring */}
        <div className="card">
          <WaterProgressRing 
            progress={progress}
            currentIntake={currentIntake}
            dailyGoal={dailyGoal}
          />
          
          <div className="text-center mt-6">
            <p className="text-lg font-medium text-gray-800 mb-2">
              {getMotivationalMessage()}
            </p>
            <div className="flex justify-center items-center gap-4 text-sm text-gray-600">
              <span>{formatVolume(currentIntake)} / {formatVolume(dailyGoal)}</span>
              {progress >= 100 && <span className="text-green-600 font-medium">✓ Goal reached!</span>}
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="card">
          <h2 className="text-lg font-semibold text-gray-800 mb-4">Quick Add</h2>
          <div className="grid grid-cols-2 gap-3">
            {[
              { amount: 250, label: 'Glass', emoji: '🥛' },
              { amount: 500, label: 'Bottle', emoji: '🍼' },
              { amount: 750, label: 'Large', emoji: '🥤' },
              { amount: 1000, label: 'Liter', emoji: '💧' }
            ].map(({ amount, label, emoji }) => (
              <button
                key={amount}
                onClick={() => handleDrinkWater(amount)}
                className="btn-water flex flex-col items-center gap-2 py-4"
              >
                <span className="text-2xl">{emoji}</span>
                <span className="font-medium">{label}</span>
                <span className="text-sm opacity-90">{amount}ml</span>
              </button>
            ))}
          </div>

          {/* Custom Amount Button */}
          <button
            onClick={() => setShowCustomModal(true)}
            className="btn-secondary w-full mt-3 flex items-center justify-center gap-2"
          >
            <span className="text-lg">⚡</span>
            Custom Amount
          </button>
        </div>

        {/* Status Cards */}
        <div className="grid grid-cols-2 gap-4">
          {/* Notification Status */}
          <div className="card">
            <div className="text-center">
              <div className="text-2xl mb-2">
                {isSupported && permission === 'granted' ? '🔔' : '🔕'}
              </div>
              <p className="text-sm font-medium text-gray-800 mb-1">
                Notifications
              </p>
              <p className="text-xs text-gray-600">
                {isSupported 
                  ? permission === 'granted' ? 'Enabled' : 'Disabled'
                  : 'Not supported'
                }
              </p>
            </div>
          </div>

          {/* Next Reminder */}
          <div className="card">
            <div className="text-center">
              <div className="text-2xl mb-2">⏰</div>
              <p className="text-sm font-medium text-gray-800 mb-1">
                Next Reminder
              </p>
              <p className="text-xs text-gray-600">
                {getTimeUntilNextReminder() || 'Not set'}
              </p>
            </div>
          </div>
        </div>

        {/* Water Intake History */}
        <WaterIntakeHistory />

        {/* Weekly Chart */}
        <WeeklyChart />

        {/* Custom Amount Modal */}
        <CustomAmountModal
          isOpen={showCustomModal}
          onClose={() => setShowCustomModal(false)}
          onConfirm={handleCustomAmount}
        />
      </div>
    </div>
  )
}
