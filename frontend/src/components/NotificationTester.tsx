import { useState, useEffect } from 'react'
import { useAuthStore } from '../stores/authStore'
import {
  getFCMToken,
  showNotification,
  initializeFirebase
} from '../services/firebase'
import { supabase } from '../services/supabase'

interface TestResult {
  test: string
  status: 'success' | 'error' | 'warning'
  message: string
  details?: any
}

export function NotificationTester() {
  const { user } = useAuthStore()
  const [results, setResults] = useState<TestResult[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [fcmToken, setFcmToken] = useState<string | null>(null)

  useEffect(() => {
    initializeFirebase()
  }, [])

  const addResult = (result: TestResult) => {
    setResults(prev => [...prev, result])
  }

  const clearResults = () => {
    setResults([])
  }

  const testNotificationPermission = async () => {
    addResult({
      test: 'Notification Permission',
      status: 'success',
      message: `Current permission: ${Notification.permission}`
    })

    if (Notification.permission === 'default') {
      try {
        const permission = await Notification.requestPermission()
        addResult({
          test: 'Permission Request',
          status: permission === 'granted' ? 'success' : 'error',
          message: `Permission ${permission}`
        })
      } catch (error) {
        addResult({
          test: 'Permission Request',
          status: 'error',
          message: `Failed: ${error instanceof Error ? error.message : 'Unknown error'}`
        })
      }
    }
  }

  const testFCMToken = async () => {
    try {
      const token = await getFCMToken()
      if (token) {
        setFcmToken(token)
        addResult({
          test: 'FCM Token Generation',
          status: 'success',
          message: 'FCM token generated successfully',
          details: { tokenLength: token.length, tokenPreview: token.substring(0, 20) + '...' }
        })

        // Update user profile with FCM token
        if (user) {
          const { error } = await supabase
            .from('users')
            .update({ fcm_token: token })
            .eq('id', user.id)

          if (error) {
            addResult({
              test: 'FCM Token Storage',
              status: 'error',
              message: `Failed to store token: ${error.message}`
            })
          } else {
            addResult({
              test: 'FCM Token Storage',
              status: 'success',
              message: 'FCM token stored in database'
            })
          }
        }
      } else {
        addResult({
          test: 'FCM Token Generation',
          status: 'error',
          message: 'Failed to generate FCM token'
        })
      }
    } catch (error) {
      addResult({
        test: 'FCM Token Generation',
        status: 'error',
        message: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`
      })
    }
  }

  const testLocalNotification = () => {
    if (Notification.permission !== 'granted') {
      addResult({
        test: 'Local Notification',
        status: 'error',
        message: 'Notification permission not granted'
      })
      return
    }

    try {
      const success = showNotification(
        '💧 AquaBell Test',
        'This is a test notification from AquaBell! Time to drink some water.',
        {
          tag: 'test-notification',
          requireInteraction: false
        }
      )

      addResult({
        test: 'Local Notification',
        status: success ? 'success' : 'error',
        message: success ? 'Local notification sent successfully' : 'Failed to send local notification'
      })
    } catch (error) {
      addResult({
        test: 'Local Notification',
        status: 'error',
        message: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`
      })
    }
  }

  const testServiceWorker = async () => {
    if (!('serviceWorker' in navigator)) {
      addResult({
        test: 'Service Worker',
        status: 'error',
        message: 'Service Worker not supported'
      })
      return
    }

    try {
      const registration = await navigator.serviceWorker.getRegistration()
      if (registration) {
        addResult({
          test: 'Service Worker',
          status: 'success',
          message: 'Service Worker is registered and active',
          details: { scope: registration.scope, state: registration.active?.state }
        })
      } else {
        addResult({
          test: 'Service Worker',
          status: 'warning',
          message: 'Service Worker not registered'
        })
      }
    } catch (error) {
      addResult({
        test: 'Service Worker',
        status: 'error',
        message: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`
      })
    }
  }

  const testEdgeFunction = async () => {
    if (!user) {
      addResult({
        test: 'Edge Function',
        status: 'error',
        message: 'User not authenticated'
      })
      return
    }

    try {
      // Test the production Edge Function
      const response = await fetch('https://odbsptiingkeezsqufor.supabase.co/functions/v1/send-reminders', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json'
        }
      })

      if (response.ok) {
        const result = await response.json()
        addResult({
          test: 'Edge Function (Production)',
          status: 'success',
          message: 'Edge Function executed successfully',
          details: result.summary
        })
      } else {
        const errorText = await response.text()
        addResult({
          test: 'Edge Function (Production)',
          status: 'error',
          message: `HTTP ${response.status}: ${errorText}`
        })
      }
    } catch (error) {
      addResult({
        test: 'Edge Function (Production)',
        status: 'error',
        message: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`
      })
    }
  }

  const runAllTests = async () => {
    setIsLoading(true)
    clearResults()

    await testNotificationPermission()
    await new Promise(resolve => setTimeout(resolve, 500))
    
    await testServiceWorker()
    await new Promise(resolve => setTimeout(resolve, 500))
    
    await testFCMToken()
    await new Promise(resolve => setTimeout(resolve, 500))
    
    testLocalNotification()
    await new Promise(resolve => setTimeout(resolve, 500))
    
    await testEdgeFunction()

    setIsLoading(false)
  }

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'success': return '✅'
      case 'error': return '❌'
      case 'warning': return '⚠️'
      default: return '❓'
    }
  }

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'success': return 'text-green-600'
      case 'error': return 'text-red-600'
      case 'warning': return 'text-yellow-600'
      default: return 'text-gray-600'
    }
  }

  if (!user) {
    return (
      <div className="p-6 bg-yellow-50 border border-yellow-200 rounded-lg">
        <p className="text-yellow-800">Please sign in to test notifications.</p>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">🔔 Notification Testing Dashboard</h2>
        <p className="text-gray-600 mb-6">
          Test all aspects of push notifications for AquaBell. This will verify Firebase configuration,
          FCM tokens, service workers, and the notification Edge Function.
        </p>

        <div className="flex flex-wrap gap-3 mb-6">
          <button
            onClick={runAllTests}
            disabled={isLoading}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50"
          >
            {isLoading ? 'Running Tests...' : 'Run All Tests'}
          </button>
          <button
            onClick={testNotificationPermission}
            className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700"
          >
            Test Permission
          </button>
          <button
            onClick={testLocalNotification}
            className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700"
          >
            Test Local Notification
          </button>
          <button
            onClick={clearResults}
            className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700"
          >
            Clear Results
          </button>
        </div>

        {fcmToken && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <h3 className="font-semibold text-blue-900 mb-2">FCM Token Generated</h3>
            <p className="text-sm text-blue-700 font-mono break-all">{fcmToken}</p>
          </div>
        )}

        <div className="space-y-3">
          {results.map((result, index) => (
            <div
              key={index}
              className="border border-gray-200 rounded-lg p-4 bg-gray-50"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <span className="text-xl">{getStatusIcon(result.status)}</span>
                  <span className="font-semibold text-gray-900">{result.test}</span>
                </div>
                <span className={`text-sm font-medium ${getStatusColor(result.status)}`}>
                  {result.status.toUpperCase()}
                </span>
              </div>
              <p className="text-gray-700 mt-2">{result.message}</p>
              {result.details && (
                <pre className="text-xs text-gray-600 mt-2 bg-white p-2 rounded border overflow-x-auto">
                  {JSON.stringify(result.details, null, 2)}
                </pre>
              )}
            </div>
          ))}
        </div>

        {results.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            No test results yet. Click "Run All Tests" to begin testing.
          </div>
        )}
      </div>
    </div>
  )
}
