import { useEffect, useState } from 'react'
import { useAuthStore } from '../stores/authStore'
import { supabase } from '../services/supabase'

export function DebugInfo() {
  const { user, profile, loading, error } = useAuthStore()
  const [debugInfo, setDebugInfo] = useState<any>({})

  useEffect(() => {
    const gatherDebugInfo = async () => {
      try {
        // Test basic Supabase connection
        const { data: healthCheck } = await supabase.from('users').select('count').limit(1)
        
        // Test auth session
        const { data: { session } } = await supabase.auth.getSession()
        
        // Environment variables
        const envVars = {
          supabaseUrl: import.meta.env.VITE_SUPABASE_URL,
          hasAnonKey: !!import.meta.env.VITE_SUPABASE_ANON_KEY,
          mode: import.meta.env.MODE
        }

        setDebugInfo({
          healthCheck: !!healthCheck,
          session: !!session,
          sessionUser: session?.user?.id || null,
          envVars,
          timestamp: new Date().toISOString()
        })
      } catch (error) {
        setDebugInfo({
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date().toISOString()
        })
      }
    }

    gatherDebugInfo()
  }, [])

  // Only show in development
  if (import.meta.env.MODE !== 'development') {
    return null
  }

  return (
    <div className="fixed bottom-4 right-4 bg-black/80 text-white p-4 rounded-lg text-xs max-w-sm z-50">
      <h3 className="font-bold mb-2">🐛 Debug Info</h3>
      
      <div className="space-y-1">
        <div>
          <strong>Auth State:</strong>
          <div className="ml-2">
            Loading: {loading ? '✅' : '❌'}<br/>
            User: {user ? '✅' : '❌'}<br/>
            Profile: {profile ? '✅' : '❌'}<br/>
            Error: {error ? '❌' : '✅'}
          </div>
        </div>

        <div>
          <strong>Supabase:</strong>
          <div className="ml-2">
            Health: {debugInfo.healthCheck ? '✅' : '❌'}<br/>
            Session: {debugInfo.session ? '✅' : '❌'}<br/>
            URL: {debugInfo.envVars?.supabaseUrl?.includes('127.0.0.1') ? 'Local' : 'Remote'}
          </div>
        </div>

        {error && (
          <div>
            <strong>Error:</strong>
            <div className="ml-2 text-red-300">{error}</div>
          </div>
        )}

        {debugInfo.error && (
          <div>
            <strong>Debug Error:</strong>
            <div className="ml-2 text-red-300">{debugInfo.error}</div>
          </div>
        )}
      </div>

      <div className="mt-2 text-xs opacity-60">
        Updated: {debugInfo.timestamp?.split('T')[1]?.split('.')[0]}
      </div>
    </div>
  )
}
