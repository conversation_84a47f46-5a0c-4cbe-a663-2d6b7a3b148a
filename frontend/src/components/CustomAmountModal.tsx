import { useState } from 'react'
import { formatVolume } from '../utils/format'

interface CustomAmountModalProps {
  isOpen: boolean
  onClose: () => void
  onConfirm: (amount: number) => void
}

export default function CustomAmountModal({ isOpen, onClose, onConfirm }: CustomAmountModalProps) {
  const [amount, setAmount] = useState('')
  const [unit, setUnit] = useState<'ml' | 'oz'>('ml')

  if (!isOpen) return null

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    const numAmount = parseFloat(amount)
    if (isNaN(numAmount) || numAmount <= 0) return

    // Convert to ml if needed
    const amountInMl = unit === 'oz' ? Math.round(numAmount * 29.5735) : numAmount
    
    onConfirm(amountInMl)
    setAmount('')
    onClose()
  }

  const handleClose = () => {
    setAmount('')
    onClose()
  }

  const presetAmounts = unit === 'ml' 
    ? [100, 200, 250, 300, 500, 750, 1000]
    : [3, 6, 8, 10, 16, 24, 32]

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-3xl p-6 w-full max-w-sm">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-800">Add Custom Amount</h2>
          <button
            onClick={handleClose}
            className="p-2 text-gray-400 hover:text-gray-600 rounded-xl transition-colors duration-200"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Unit Toggle */}
          <div className="flex bg-gray-100 rounded-2xl p-1">
            <button
              type="button"
              onClick={() => setUnit('ml')}
              className={`flex-1 py-2 px-4 rounded-xl text-sm font-medium transition-all duration-200 ${
                unit === 'ml' 
                  ? 'bg-white text-primary-600 shadow-sm' 
                  : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              Milliliters (ml)
            </button>
            <button
              type="button"
              onClick={() => setUnit('oz')}
              className={`flex-1 py-2 px-4 rounded-xl text-sm font-medium transition-all duration-200 ${
                unit === 'oz' 
                  ? 'bg-white text-primary-600 shadow-sm' 
                  : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              Fluid Ounces (oz)
            </button>
          </div>

          {/* Amount Input */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Amount ({unit})
            </label>
            <input
              type="number"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
              placeholder={`Enter amount in ${unit}`}
              className="input-field text-lg text-center"
              min="1"
              step={unit === 'ml' ? '1' : '0.1'}
              autoFocus
            />
          </div>

          {/* Preset Amounts */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Quick Select
            </label>
            <div className="grid grid-cols-4 gap-2">
              {presetAmounts.map((preset) => (
                <button
                  key={preset}
                  type="button"
                  onClick={() => setAmount(preset.toString())}
                  className="py-2 px-3 bg-gray-50 hover:bg-primary-50 hover:text-primary-600 rounded-xl text-sm font-medium transition-all duration-200"
                >
                  {preset}{unit}
                </button>
              ))}
            </div>
          </div>

          {/* Preview */}
          {amount && !isNaN(parseFloat(amount)) && (
            <div className="p-3 bg-water-50 rounded-2xl">
              <p className="text-sm text-water-700 text-center">
                Adding: <span className="font-semibold">
                  {formatVolume(unit === 'oz' ? Math.round(parseFloat(amount) * 29.5735) : parseFloat(amount))}
                </span>
              </p>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex gap-3">
            <button
              type="button"
              onClick={handleClose}
              className="btn-secondary flex-1"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={!amount || isNaN(parseFloat(amount)) || parseFloat(amount) <= 0}
              className="btn-primary flex-1 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Add Water
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
