// FCM Testing Guide Component
import { useState } from 'react'

export default function FCMTestingGuide() {
  const [isExpanded, setIsExpanded] = useState(false)

  return (
    <div className="bg-blue-50 border border-blue-200 rounded-xl p-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <span className="text-2xl">📖</span>
          <div>
            <h3 className="font-semibold text-blue-800">FCM Testing Guide</h3>
            <p className="text-sm text-blue-600">Learn how to test Firebase Cloud Messaging</p>
          </div>
        </div>
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="text-blue-600 hover:text-blue-800 font-medium"
        >
          {isExpanded ? 'Hide Guide' : 'Show Guide'}
        </button>
      </div>

      {isExpanded && (
        <div className="mt-6 space-y-4 text-sm text-blue-800">
          <div>
            <h4 className="font-semibold mb-2">🔧 Setup Requirements</h4>
            <ul className="list-disc list-inside space-y-1 text-blue-700">
              <li>Firebase project with Cloud Messaging enabled</li>
              <li>Valid Firebase configuration in environment variables</li>
              <li>Service worker registered for background notifications</li>
              <li>Notification permissions granted by user</li>
            </ul>
          </div>

          <div>
            <h4 className="font-semibold mb-2">🧪 Testing Methods</h4>
            <div className="space-y-3">
              <div className="bg-white p-3 rounded-lg border border-blue-200">
                <h5 className="font-medium text-blue-800">1. Local Notifications</h5>
                <p className="text-blue-600 text-xs mt-1">
                  Test browser notification API without FCM. Works immediately if permissions are granted.
                </p>
              </div>
              
              <div className="bg-white p-3 rounded-lg border border-blue-200">
                <h5 className="font-medium text-blue-800">2. FCM via Backend</h5>
                <p className="text-blue-600 text-xs mt-1">
                  Send notifications through a backend service using Firebase Admin SDK. Most reliable method.
                </p>
              </div>
              
              <div className="bg-white p-3 rounded-lg border border-blue-200">
                <h5 className="font-medium text-blue-800">3. Firebase Console</h5>
                <p className="text-blue-600 text-xs mt-1">
                  Use Firebase Console → Cloud Messaging → Send test message. Requires FCM token.
                </p>
              </div>
            </div>
          </div>

          <div>
            <h4 className="font-semibold mb-2">🔍 Troubleshooting</h4>
            <div className="space-y-2 text-blue-700">
              <div className="flex items-start gap-2">
                <span className="text-red-500 font-bold">❌</span>
                <div>
                  <p className="font-medium">Service Worker Error</p>
                  <p className="text-xs">Check if firebase-messaging-sw.js is properly registered and accessible</p>
                </div>
              </div>
              
              <div className="flex items-start gap-2">
                <span className="text-red-500 font-bold">❌</span>
                <div>
                  <p className="font-medium">No FCM Token</p>
                  <p className="text-xs">Ensure notifications are enabled and Firebase is properly configured</p>
                </div>
              </div>
              
              <div className="flex items-start gap-2">
                <span className="text-red-500 font-bold">❌</span>
                <div>
                  <p className="font-medium">Notifications Not Appearing</p>
                  <p className="text-xs">Check browser notification settings and Do Not Disturb mode</p>
                </div>
              </div>
            </div>
          </div>

          <div>
            <h4 className="font-semibold mb-2">🚀 Testing with Firebase Console</h4>
            <ol className="list-decimal list-inside space-y-1 text-blue-700 text-xs">
              <li>Go to Firebase Console → Your Project → Cloud Messaging</li>
              <li>Click "Send your first message"</li>
              <li>Enter notification title and text</li>
              <li>In "Target" section, select "FCM registration token"</li>
              <li>Copy your FCM token from the test page and paste it</li>
              <li>Click "Test" to send the notification</li>
            </ol>
          </div>

          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
            <div className="flex items-start gap-2">
              <span className="text-yellow-600">⚠️</span>
              <div className="text-yellow-800 text-xs">
                <p className="font-medium">Development Note</p>
                <p>
                  For production apps, implement a proper backend service to send notifications. 
                  Never expose Firebase server keys in client-side code.
                </p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
