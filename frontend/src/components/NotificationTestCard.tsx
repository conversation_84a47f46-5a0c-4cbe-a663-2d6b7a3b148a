import { useState } from 'react'
import { useNotificationStore } from '../stores/notificationStore'
import { 
  showTestNotification, 
  scheduleLocalNotification, 
  checkNotificationHealth,
  formatNotificationPermission
} from '../utils/notifications'
import LoadingSpinner from './LoadingSpinner'

export default function NotificationTestCard() {
  const { permission, requestPermission } = useNotificationStore()
  const [testing, setTesting] = useState(false)
  const [healthCheck, setHealthCheck] = useState<any>(null)
  const [showHealthCheck, setShowHealthCheck] = useState(false)

  const handleTestNotification = async () => {
    setTesting(true)
    try {
      const success = await showTestNotification()
      if (success) {
        console.log('Test notification sent successfully')
      } else {
        console.log('Failed to send test notification')
      }
    } catch (error) {
      console.error('Error sending test notification:', error)
    } finally {
      setTesting(false)
    }
  }

  const handleScheduleTest = () => {
    scheduleLocalNotification(1) // 1 minute
  }

  const handleHealthCheck = async () => {
    const health = await checkNotificationHealth()
    setHealthCheck(health)
    setShowHealthCheck(true)
  }

  const permissionInfo = formatNotificationPermission(permission)

  return (
    <div className="card">
      <h3 className="text-lg font-semibold text-gray-800 mb-4">
        🔔 Notification Testing
      </h3>

      {/* Permission Status */}
      <div className="mb-4 p-3 bg-gray-50 rounded-2xl">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium text-gray-700">Status:</span>
          <div className="flex items-center gap-2">
            <span className="text-lg">{permissionInfo.icon}</span>
            <span className={`text-sm font-medium ${permissionInfo.color}`}>
              {permissionInfo.text}
            </span>
          </div>
        </div>
      </div>

      {/* Test Actions */}
      <div className="space-y-3">
        {permission !== 'granted' ? (
          <button
            onClick={requestPermission}
            className="btn-primary w-full"
          >
            Enable Notifications
          </button>
        ) : (
          <>
            <button
              onClick={handleTestNotification}
              disabled={testing}
              className="btn-water w-full flex items-center justify-center gap-2"
            >
              {testing && <LoadingSpinner size="small" />}
              Send Test Notification
            </button>

            <button
              onClick={handleScheduleTest}
              className="btn-secondary w-full"
            >
              Schedule Test (1 min)
            </button>
          </>
        )}

        <button
          onClick={handleHealthCheck}
          className="btn-secondary w-full"
        >
          Check Health
        </button>
      </div>

      {/* Health Check Results */}
      {showHealthCheck && healthCheck && (
        <div className="mt-4 p-3 bg-blue-50 rounded-2xl">
          <h4 className="font-medium text-blue-800 mb-2">Health Check Results</h4>
          <div className="space-y-1 text-sm">
            <div className="flex justify-between">
              <span>Notifications Supported:</span>
              <span className={healthCheck.isSupported ? 'text-green-600' : 'text-red-600'}>
                {healthCheck.isSupported ? '✅' : '❌'}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Permission:</span>
              <span className={`${formatNotificationPermission(healthCheck.permission).color}`}>
                {formatNotificationPermission(healthCheck.permission).text}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Service Worker:</span>
              <span className={healthCheck.serviceWorkerRegistered ? 'text-green-600' : 'text-red-600'}>
                {healthCheck.serviceWorkerRegistered ? '✅' : '❌'}
              </span>
            </div>
            <div className="flex justify-between">
              <span>FCM Supported:</span>
              <span className={healthCheck.fcmSupported ? 'text-green-600' : 'text-red-600'}>
                {healthCheck.fcmSupported ? '✅' : '❌'}
              </span>
            </div>
          </div>
          <button
            onClick={() => setShowHealthCheck(false)}
            className="mt-2 text-xs text-blue-600 hover:text-blue-800"
          >
            Hide Results
          </button>
        </div>
      )}

      {/* Instructions */}
      <div className="mt-4 p-3 bg-yellow-50 rounded-2xl">
        <p className="text-xs text-yellow-800">
          <strong>Note:</strong> Test notifications will appear immediately. 
          Make sure your browser allows notifications and you're not in Do Not Disturb mode.
        </p>
      </div>
    </div>
  )
}
