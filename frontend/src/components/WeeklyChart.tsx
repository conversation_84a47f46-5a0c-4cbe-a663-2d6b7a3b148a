import { useWaterIntake } from '../hooks/useWaterIntake'
import { formatVolume } from '../utils/format'

interface WeeklyChartProps {
  className?: string
}

export default function WeeklyChart({ className = '' }: WeeklyChartProps) {
  const { getWeeklyData } = useWaterIntake()
  const weeklyData = getWeeklyData()

  const maxAmount = Math.max(...weeklyData.map(d => Math.max(d.amount, d.goal)))
  const chartHeight = 120

  const getDayLabel = (dateStr: string) => {
    const date = new Date(dateStr)
    return date.toLocaleDateString('en-US', { weekday: 'short' })
  }

  const getBarHeight = (amount: number) => {
    return (amount / maxAmount) * chartHeight
  }

  const getProgressColor = (amount: number, goal: number) => {
    const percentage = (amount / goal) * 100
    if (percentage >= 100) return 'bg-green-500'
    if (percentage >= 75) return 'bg-water-500'
    if (percentage >= 50) return 'bg-yellow-500'
    if (percentage >= 25) return 'bg-orange-500'
    return 'bg-red-400'
  }

  return (
    <div className={`card ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-800">Weekly Progress</h3>
        <div className="text-sm text-gray-500">Last 7 days</div>
      </div>

      <div className="flex items-end justify-between gap-2" style={{ height: chartHeight + 40 }}>
        {weeklyData.map((day, index) => {
          const barHeight = getBarHeight(day.amount)
          const goalHeight = getBarHeight(day.goal)
          const isToday = index === weeklyData.length - 1

          return (
            <div key={day.date} className="flex-1 flex flex-col items-center">
              {/* Bar container */}
              <div className="relative w-full flex flex-col items-center" style={{ height: chartHeight }}>
                {/* Goal line */}
                <div
                  className="absolute w-full border-t-2 border-dashed border-gray-300"
                  style={{ bottom: goalHeight }}
                />
                
                {/* Progress bar */}
                <div
                  className={`w-full rounded-t-lg transition-all duration-500 ${getProgressColor(day.amount, day.goal)} ${
                    isToday ? 'ring-2 ring-primary-300' : ''
                  }`}
                  style={{ 
                    height: barHeight,
                    marginTop: chartHeight - barHeight
                  }}
                />
              </div>

              {/* Day label */}
              <div className="mt-2 text-center">
                <div className={`text-xs font-medium ${isToday ? 'text-primary-600' : 'text-gray-600'}`}>
                  {getDayLabel(day.date)}
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  {formatVolume(day.amount)}
                </div>
              </div>
            </div>
          )
        })}
      </div>

      {/* Legend */}
      <div className="mt-4 flex items-center justify-center gap-4 text-xs text-gray-600">
        <div className="flex items-center gap-1">
          <div className="w-3 h-3 bg-water-500 rounded"></div>
          <span>Intake</span>
        </div>
        <div className="flex items-center gap-1">
          <div className="w-3 h-1 border-t-2 border-dashed border-gray-300"></div>
          <span>Goal</span>
        </div>
      </div>

      {/* Summary */}
      <div className="mt-4 p-3 bg-gray-50 rounded-2xl">
        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <div className="text-lg font-semibold text-gray-800">
              {weeklyData.filter(d => d.amount >= d.goal).length}
            </div>
            <div className="text-xs text-gray-600">Goals met</div>
          </div>
          <div>
            <div className="text-lg font-semibold text-gray-800">
              {formatVolume(weeklyData.reduce((sum, d) => sum + d.amount, 0))}
            </div>
            <div className="text-xs text-gray-600">Total intake</div>
          </div>
          <div>
            <div className="text-lg font-semibold text-gray-800">
              {Math.round(weeklyData.reduce((sum, d) => sum + d.amount, 0) / weeklyData.length)}ml
            </div>
            <div className="text-xs text-gray-600">Daily average</div>
          </div>
        </div>
      </div>
    </div>
  )
}
