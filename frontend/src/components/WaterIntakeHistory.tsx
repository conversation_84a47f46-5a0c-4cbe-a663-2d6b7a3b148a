import { formatVolume } from '../utils/format'
import { useWaterIntake } from '../hooks/useWaterIntake'

interface WaterIntakeHistoryProps {
  className?: string
}

export default function WaterIntakeHistory({ className = '' }: WaterIntakeHistoryProps) {
  const { todaysEntries, removeWaterIntake } = useWaterIntake()

  if (todaysEntries.length === 0) {
    return (
      <div className={`card ${className}`}>
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Today's Intake</h3>
        <div className="text-center py-8">
          <div className="text-4xl mb-2">💧</div>
          <p className="text-gray-500">No water logged yet today</p>
          <p className="text-sm text-gray-400 mt-1">Start by adding your first drink!</p>
        </div>
      </div>
    )
  }

  const getTimeString = (timestamp: Date) => {
    return new Date(timestamp).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit'
    })
  }

  const getEntryIcon = (type: string) => {
    switch (type) {
      case 'quick_add':
        return '⚡'
      case 'manual':
        return '✏️'
      default:
        return '💧'
    }
  }

  return (
    <div className={`card ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-800">Today's Intake</h3>
        <span className="text-sm text-gray-500">{todaysEntries.length} entries</span>
      </div>

      <div className="space-y-3 max-h-64 overflow-y-auto">
        {todaysEntries.map((entry) => (
          <div
            key={entry.id}
            className="flex items-center justify-between p-3 bg-water-50 rounded-2xl hover:bg-water-100 transition-colors duration-200"
          >
            <div className="flex items-center gap-3">
              <div className="text-xl">{getEntryIcon(entry.type)}</div>
              <div>
                <p className="font-medium text-gray-800">
                  {formatVolume(entry.amount)}
                </p>
                <p className="text-sm text-gray-500">
                  {getTimeString(entry.timestamp)}
                </p>
              </div>
            </div>

            <button
              onClick={() => removeWaterIntake(entry.id)}
              className="p-2 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded-xl transition-all duration-200"
              title="Remove entry"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        ))}
      </div>

      {todaysEntries.length > 5 && (
        <div className="mt-3 text-center">
          <p className="text-xs text-gray-500">Scroll to see more entries</p>
        </div>
      )}
    </div>
  )
}
