import { getRandomReminder } from '@aquabell/shared'

// Service worker message types
export interface ServiceWorkerMessage {
  type: 'WATER_LOGGED' | 'REMINDER_SNOOZED' | 'SKIP_WAITING'
  timestamp?: number
  amount?: number
  snoozeMinutes?: number
}

// Listen for service worker messages
export function setupServiceWorkerListener(
  onWaterLogged?: (amount: number) => void,
  onReminderSnoozed?: (minutes: number) => void
) {
  if ('serviceWorker' in navigator) {
    navigator.serviceWorker.addEventListener('message', (event) => {
      const message: ServiceWorkerMessage = event.data
      
      switch (message.type) {
        case 'WATER_LOGGED':
          if (onWaterLogged && message.amount) {
            onWaterLogged(message.amount)
          }
          break
        case 'REMINDER_SNOOZED':
          if (onReminderSnoozed && message.snoozeMinutes) {
            onReminderSnoozed(message.snoozeMinutes)
          }
          break
      }
    })
  }
}

// Show a test notification
export async function showTestNotification(): Promise<boolean> {
  if (!('Notification' in window)) {
    console.warn('Notifications not supported')
    return false
  }

  if (Notification.permission !== 'granted') {
    console.warn('Notification permission not granted')
    return false
  }

  try {
    const reminder = getRandomReminder()
    
    const notification = new Notification(reminder.title, {
      body: reminder.body,
      icon: '/icons/icon-192x192.png',
      badge: '/icons/badge-72x72.png',
      tag: 'test-notification',
      requireInteraction: false,
      // Note: actions are not supported in standard NotificationOptions
      // They would need to be handled via service worker
    })

    // Auto close after 5 seconds
    setTimeout(() => {
      notification.close()
    }, 5000)

    return true
  } catch (error) {
    console.error('Error showing test notification:', error)
    return false
  }
}

// Schedule a local notification (for testing)
export function scheduleLocalNotification(delayMinutes: number = 1): void {
  if (!('Notification' in window) || Notification.permission !== 'granted') {
    console.warn('Cannot schedule notification: permission not granted')
    return
  }

  setTimeout(() => {
    const reminder = getRandomReminder()
    
    new Notification(reminder.title, {
      body: reminder.body,
      icon: '/icons/icon-192x192.png',
      badge: '/icons/badge-72x72.png',
      tag: 'scheduled-reminder',
      requireInteraction: false
    })
  }, delayMinutes * 60 * 1000)

  console.log(`Local notification scheduled for ${delayMinutes} minute(s)`)
}

// Get notification permission status with user-friendly text
export function getNotificationStatusText(): string {
  if (!('Notification' in window)) {
    return 'Not supported on this device'
  }

  switch (Notification.permission) {
    case 'granted':
      return 'Enabled'
    case 'denied':
      return 'Blocked'
    case 'default':
      return 'Not requested'
    default:
      return 'Unknown'
  }
}

// Check if notifications are working properly
export async function checkNotificationHealth(): Promise<{
  isSupported: boolean
  permission: NotificationPermission
  serviceWorkerRegistered: boolean
  fcmSupported: boolean
}> {
  const isSupported = 'Notification' in window
  const permission = isSupported ? Notification.permission : 'denied'
  
  let serviceWorkerRegistered = false
  if ('serviceWorker' in navigator) {
    try {
      const registration = await navigator.serviceWorker.getRegistration()
      serviceWorkerRegistered = !!registration
    } catch (error) {
      console.error('Error checking service worker:', error)
    }
  }

  const fcmSupported = 'serviceWorker' in navigator && 'PushManager' in window

  return {
    isSupported,
    permission,
    serviceWorkerRegistered,
    fcmSupported
  }
}

// Format notification permission for display
export function formatNotificationPermission(permission: NotificationPermission): {
  text: string
  color: string
  icon: string
} {
  switch (permission) {
    case 'granted':
      return {
        text: 'Enabled',
        color: 'text-green-600',
        icon: '✅'
      }
    case 'denied':
      return {
        text: 'Blocked',
        color: 'text-red-600',
        icon: '❌'
      }
    case 'default':
      return {
        text: 'Not requested',
        color: 'text-yellow-600',
        icon: '⚠️'
      }
    default:
      return {
        text: 'Unknown',
        color: 'text-gray-600',
        icon: '❓'
      }
  }
}
