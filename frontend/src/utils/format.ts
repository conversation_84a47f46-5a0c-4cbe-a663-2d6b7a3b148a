// Temporary local utilities to work around shared package build issues

/**
 * Convert milliliters to user-friendly format
 */
export function formatVolume(ml: number): string {
  if (ml >= 1000) {
    return `${(ml / 1000).toFixed(1)}L`;
  }
  return `${ml}ml`;
}

/**
 * Generate a random hydration reminder from predefined list
 */
export function getRandomReminder(): { title: string; body: string; emoji: string } {
  const reminders = [
    {
      title: "Time to hydrate! 💧",
      body: "Your body is calling for some refreshing water!",
      emoji: "💧"
    },
    {
      title: "Water break! 🌊",
      body: "Take a moment to drink some water and feel refreshed!",
      emoji: "🌊"
    },
    {
      title: "Hydration station! 🚰",
      body: "Keep your energy up with a nice glass of water!",
      emoji: "🚰"
    },
    {
      title: "Drink up, buttercup! 🌻",
      body: "Your future self will thank you for staying hydrated!",
      emoji: "🌻"
    },
    {
      title: "H2O time! 💦",
      body: "Let's keep those hydration levels topped up!",
      emoji: "💦"
    },
    {
      title: "Splash of wellness! 🌈",
      body: "Every sip brings you closer to your daily goal!",
      emoji: "🌈"
    },
    {
      title: "Aqua alert! 🔔",
      body: "Time for a refreshing water moment!",
      emoji: "🔔"
    },
    {
      title: "Hydration hero! 🦸‍♀️",
      body: "Be your own superhero - drink some water!",
      emoji: "🦸‍♀️"
    }
  ];
  
  return reminders[Math.floor(Math.random() * reminders.length)];
}
