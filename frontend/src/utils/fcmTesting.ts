// FCM Testing Utilities for Development
import { getFCMToken } from '../services/firebase'

interface FCMTestPayload {
  title?: string
  body?: string
  data?: Record<string, string>
  fcmToken?: string
}

interface FCMTestResult {
  success: boolean
  messageId?: string
  error?: string
  details?: any
}

/**
 * Send a test notification using FCM
 * This function attempts multiple methods for testing FCM notifications
 */
export async function sendTestFCMNotification(payload: FCMTestPayload = {}): Promise<FCMTestResult> {
  try {
    // Get FCM token if not provided
    const fcmToken = payload.fcmToken || await getFCMToken()
    
    if (!fcmToken) {
      return {
        success: false,
        error: 'No FCM token available. Make sure notifications are enabled.'
      }
    }

    const testPayload = {
      fcmToken,
      title: payload.title || 'AquaBell Test Notification',
      body: payload.body || 'This is a test notification from AquaBell! 💧',
      data: {
        type: 'test_notification',
        timestamp: new Date().toISOString(),
        ...payload.data
      }
    }

    // Method 1: Try Supabase Edge Function (if available)
    try {
      const supabaseResponse = await fetch('/functions/v1/send-test-notification', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`
        },
        body: JSON.stringify(testPayload)
      })

      if (supabaseResponse.ok) {
        const result = await supabaseResponse.json()
        return {
          success: true,
          messageId: result.messageId,
          details: result
        }
      }
    } catch (error) {
      console.log('Supabase Edge Function not available, trying alternative methods...')
    }

    // Method 2: Try local development proxy
    try {
      const proxyResponse = await fetch('/api/send-test-notification', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(testPayload)
      })

      if (proxyResponse.ok) {
        const result = await proxyResponse.json()
        return {
          success: true,
          messageId: result.messageId,
          details: result
        }
      }
    } catch (error) {
      console.log('Local proxy not available, trying direct FCM...')
    }

    // Method 3: Direct FCM API call (requires CORS proxy in production)
    try {
      const fcmServerKey = import.meta.env.VITE_FIREBASE_SERVER_KEY
      
      if (fcmServerKey) {
        const fcmPayload = {
          to: fcmToken,
          notification: {
            title: testPayload.title,
            body: testPayload.body,
            icon: '/icons/icon-192x192.png',
            badge: '/icons/badge-72x72.png',
            tag: 'test-notification'
          },
          data: testPayload.data,
          webpush: {
            headers: {
              Urgency: 'high'
            },
            notification: {
              title: testPayload.title,
              body: testPayload.body,
              icon: '/icons/icon-192x192.png',
              badge: '/icons/badge-72x72.png',
              tag: 'test-notification',
              requireInteraction: false
            }
          }
        }

        const fcmResponse = await fetch('https://fcm.googleapis.com/fcm/send', {
          method: 'POST',
          headers: {
            'Authorization': `key=${fcmServerKey}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(fcmPayload),
        })

        if (fcmResponse.ok) {
          const result = await fcmResponse.json()
          return {
            success: true,
            messageId: result.message_id,
            details: result
          }
        } else {
          const error = await fcmResponse.json()
          return {
            success: false,
            error: `FCM API Error: ${error.error || 'Unknown error'}`,
            details: error
          }
        }
      }
    } catch (error) {
      console.log('Direct FCM call failed:', error)
    }

    // Method 4: Simulate notification for testing UI
    return {
      success: true,
      messageId: `mock_${Date.now()}`,
      details: {
        note: 'This is a simulated notification for testing purposes. To send real notifications, configure a backend service.',
        payload: testPayload
      }
    }

  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
      details: error
    }
  }
}

/**
 * Test FCM token generation and validation
 */
export async function testFCMTokenGeneration(): Promise<{
  success: boolean
  token?: string
  error?: string
}> {
  try {
    const token = await getFCMToken()
    
    if (!token) {
      return {
        success: false,
        error: 'Failed to generate FCM token'
      }
    }

    // Validate token format (basic check)
    if (typeof token !== 'string' || token.length < 100) {
      return {
        success: false,
        error: 'Invalid FCM token format'
      }
    }

    return {
      success: true,
      token
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

/**
 * Test service worker registration for FCM
 */
export async function testServiceWorkerForFCM(): Promise<{
  success: boolean
  registration?: ServiceWorkerRegistration
  error?: string
}> {
  try {
    if (!('serviceWorker' in navigator)) {
      return {
        success: false,
        error: 'Service workers not supported in this browser'
      }
    }

    // Check if Firebase messaging service worker is registered
    const registration = await navigator.serviceWorker.getRegistration('/firebase-messaging-sw.js')
    
    if (!registration) {
      return {
        success: false,
        error: 'Firebase messaging service worker not registered'
      }
    }

    return {
      success: true,
      registration
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

/**
 * Comprehensive FCM testing suite
 */
export async function runFCMTestSuite(): Promise<{
  tokenTest: Awaited<ReturnType<typeof testFCMTokenGeneration>>
  serviceWorkerTest: Awaited<ReturnType<typeof testServiceWorkerForFCM>>
  notificationTest: Awaited<ReturnType<typeof sendTestFCMNotification>>
}> {
  const [tokenTest, serviceWorkerTest, notificationTest] = await Promise.all([
    testFCMTokenGeneration(),
    testServiceWorkerForFCM(),
    sendTestFCMNotification()
  ])

  return {
    tokenTest,
    serviceWorkerTest,
    notificationTest
  }
}
