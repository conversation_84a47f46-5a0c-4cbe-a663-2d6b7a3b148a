name: Test

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test-shared:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: |
        cd shared
        npm ci
    
    - name: Run tests
      run: |
        cd shared
        npm test
    
    - name: Run linting
      run: |
        cd shared
        npm run lint

  test-frontend:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: |
        cd shared && npm ci
        cd ../frontend && npm ci
    
    - name: Build shared package
      run: |
        cd shared
        npm run build
    
    - name: Run tests
      run: |
        cd frontend
        npm test
    
    - name: Run linting
      run: |
        cd frontend
        npm run lint
    
    - name: Build frontend
      run: |
        cd frontend
        npm run build

  type-check:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: |
        cd shared && npm ci
        cd ../frontend && npm ci
    
    - name: Build shared package
      run: |
        cd shared
        npm run build
    
    - name: Type check frontend
      run: |
        cd frontend
        npx tsc --noEmit
