#!/usr/bin/env node

/**
 * FCM Testing Script for AquaBell
 *
 * This script can be used to test Firebase Cloud Messaging notifications
 * from the backend/server side using the legacy FCM API.
 *
 * Usage:
 *   node test-fcm.js <FCM_TOKEN>
 *
 * Requirements:
 *   - Firebase Server Key set in FIREBASE_SERVER_KEY environment variable
 *   - FCM token from the frontend app
 *
 * Note: If you get 404 errors, the legacy API might be deprecated for your project.
 * In that case, you'll need to use the HTTP v1 API with OAuth 2.0 tokens.
 */

const https = require('https')

// Configuration
const FIREBASE_SERVER_KEY = process.env.FIREBASE_SERVER_KEY
const FCM_ENDPOINT = 'https://fcm.googleapis.com/fcm/send'

// Get FCM token from command line arguments
const fcmToken = process.argv[2]

if (!fcmToken) {
  console.error('❌ Error: FCM token is required')
  console.log('Usage: node test-fcm.js <FCM_TOKEN>')
  console.log('Get your FCM token from the AquaBell Test Notifications page')
  process.exit(1)
}

if (!FIREBASE_SERVER_KEY) {
  console.error('❌ Error: FIREBASE_SERVER_KEY environment variable is required')
  console.log('Set your Firebase Server Key:')
  console.log('export FIREBASE_SERVER_KEY="your_server_key_here"')
  process.exit(1)
}

// FCM payload
const fcmPayload = {
  to: fcmToken,
  notification: {
    title: 'AquaBell Backend Test 💧',
    body: 'This notification was sent from the backend server!',
    icon: '/icons/icon-192x192.png',
    badge: '/icons/badge-72x72.png',
    tag: 'backend-test',
    click_action: 'https://localhost:3000'
  },
  data: {
    type: 'backend_test',
    timestamp: new Date().toISOString(),
    source: 'backend_script'
  },
  webpush: {
    headers: {
      Urgency: 'high'
    },
    notification: {
      title: 'AquaBell Backend Test 💧',
      body: 'This notification was sent from the backend server!',
      icon: '/icons/icon-192x192.png',
      badge: '/icons/badge-72x72.png',
      tag: 'backend-test',
      requireInteraction: false,
      actions: [
        {
          action: 'open_app',
          title: 'Open AquaBell'
        },
        {
          action: 'dismiss',
          title: 'Dismiss'
        }
      ]
    }
  }
}

// Send notification
function sendFCMNotification() {
  const postData = JSON.stringify(fcmPayload)

  const options = {
    hostname: 'fcm.googleapis.com',
    port: 443,
    path: '/fcm/send',
    method: 'POST',
    headers: {
      'Authorization': `key=${FIREBASE_SERVER_KEY}`,
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData)
    }
  }

  console.log('🚀 Sending FCM notification...')
  console.log(`📱 Target token: ${fcmToken.substring(0, 20)}...`)
  console.log(`🔗 URL: https://${options.hostname}${options.path}`)
  console.log(`🔑 Auth: ${options.headers.Authorization.substring(0, 20)}...`)
  console.log(`📦 Payload:`, JSON.stringify(fcmPayload, null, 2))

  const req = https.request(options, (res) => {
    console.log(`📡 Response Status: ${res.statusCode}`)
    console.log(`📡 Response Headers:`, res.headers)
    let responseData = ''

    res.on('data', (chunk) => {
      responseData += chunk
    })

    res.on('end', () => {
      console.log(`📄 Raw response length: ${responseData.length}`)

      // Check if response looks like HTML (404 error page)
      if (responseData.trim().startsWith('<')) {
        console.error('❌ Received HTML response instead of JSON (likely 404 error)')
        console.error('🔍 This suggests the FCM endpoint URL is incorrect or the API has changed')
        console.error('Raw response:', responseData.substring(0, 200) + '...')
        return
      }

      try {
        const response = JSON.parse(responseData)

        if (res.statusCode === 200) {
          console.log('✅ Notification sent successfully!')
          console.log('📊 Response:', JSON.stringify(response, null, 2))

          if (response.success === 1) {
            console.log('🎉 Notification delivered successfully')
            console.log(`📧 Message ID: ${response.results[0].message_id}`)
          } else if (response.failure === 1) {
            console.log('❌ Notification delivery failed')
            console.log('🔍 Error:', response.results[0].error)
          }
        } else {
          console.error('❌ FCM API Error:')
          console.error(`Status: ${res.statusCode}`)
          console.error('Response:', JSON.stringify(response, null, 2))
        }
      } catch (error) {
        console.error('❌ Error parsing JSON response:', error.message)
        console.error('Raw response:', responseData)
      }
    })
  })

  req.on('error', (error) => {
    console.error('❌ Request error:', error.message)
  })

  req.write(postData)
  req.end()
}

// Validate FCM token format
function validateFCMToken(token) {
  if (typeof token !== 'string') {
    return false
  }
  
  // Basic validation - FCM tokens are typically 152+ characters
  if (token.length < 100) {
    return false
  }
  
  // FCM tokens are base64-like strings
  const fcmTokenPattern = /^[A-Za-z0-9_-]+$/
  return fcmTokenPattern.test(token.replace(/:/g, ''))
}

// Main execution
console.log('🧪 AquaBell FCM Testing Script')
console.log('================================')

if (!validateFCMToken(fcmToken)) {
  console.error('❌ Invalid FCM token format')
  console.log('Make sure you copied the complete token from the test page')
  process.exit(1)
}

sendFCMNotification()

// Additional test functions
function sendWaterReminderNotification() {
  const reminderPayload = {
    ...fcmPayload,
    notification: {
      title: '💧 Time to Hydrate!',
      body: 'Don\'t forget to drink some water. Your body will thank you!',
      icon: '/icons/icon-192x192.png',
      badge: '/icons/badge-72x72.png',
      tag: 'water-reminder'
    },
    data: {
      type: 'water_reminder',
      timestamp: new Date().toISOString(),
      reminder_type: 'scheduled'
    }
  }
  
  // This function can be called separately for testing water reminders
  console.log('💧 Water reminder payload ready:', JSON.stringify(reminderPayload, null, 2))
}

function sendAchievementNotification() {
  const achievementPayload = {
    ...fcmPayload,
    notification: {
      title: '🏆 Achievement Unlocked!',
      body: 'Congratulations! You\'ve reached your daily hydration goal!',
      icon: '/icons/icon-192x192.png',
      badge: '/icons/badge-72x72.png',
      tag: 'achievement'
    },
    data: {
      type: 'achievement',
      achievement_id: 'daily_goal_reached',
      timestamp: new Date().toISOString()
    }
  }
  
  // This function can be called separately for testing achievements
  console.log('🏆 Achievement payload ready:', JSON.stringify(achievementPayload, null, 2))
}
