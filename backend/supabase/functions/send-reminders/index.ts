import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface FCMMessage {
  to: string
  notification: {
    title: string
    body: string
    icon?: string
  }
  data?: Record<string, string>
}

interface User {
  id: string
  fcm_token: string | null
  timezone: string
  daily_goal_ml: number
  wake_time: string
  sleep_time: string
  reminder_interval_minutes: number
  is_notifications_enabled: boolean
  last_reminder_at?: string
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const firebaseServerKey = Deno.env.get('FIREBASE_SERVER_KEY')!
    const firebaseProjectId = Deno.env.get('FIREBASE_PROJECT_ID')!

    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error('Missing Supabase configuration')
    }

    if (!firebaseServerKey || !firebaseProjectId) {
      console.warn('Firebase configuration missing - notifications will not be sent')
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Get users who need reminders
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select(`
        id,
        fcm_token,
        timezone,
        user_preferences!inner(
          daily_goal_ml,
          wake_time,
          sleep_time,
          reminder_interval_minutes,
          is_notifications_enabled
        )
      `)
      .eq('is_active', true)
      .eq('user_preferences.is_notifications_enabled', true)
      .not('fcm_token', 'is', null)

    if (usersError) {
      throw new Error(`Failed to fetch users: ${usersError.message}`)
    }

    const results = {
      timestamp: new Date().toISOString(),
      totalUsers: users?.length || 0,
      sent: 0,
      skipped: 0,
      errors: 0,
      details: [] as Array<{ userId: string, status: string, reason?: string }>
    }

    if (!users || users.length === 0) {
      return new Response(
        JSON.stringify({
          success: true,
          message: 'No users found for reminders',
          summary: results
        }),
        { 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200 
        }
      )
    }

    // Process each user
    for (const user of users) {
      try {
        const userPrefs = user.user_preferences[0]
        if (!userPrefs) {
          results.skipped++
          results.details.push({
            userId: user.id,
            status: 'skipped',
            reason: 'No preferences found'
          })
          continue
        }

        // Check if user is in active hours (simplified - using UTC for now)
        const now = new Date()
        const currentHour = now.getUTCHours()
        const wakeHour = parseInt(userPrefs.wake_time.split(':')[0])
        const sleepHour = parseInt(userPrefs.sleep_time.split(':')[0])

        if (currentHour < wakeHour || currentHour >= sleepHour) {
          results.skipped++
          results.details.push({
            userId: user.id,
            status: 'skipped',
            reason: 'Outside active hours'
          })
          continue
        }

        // Check last reminder time
        const { data: lastReminder } = await supabase
          .from('reminders')
          .select('sent_at')
          .eq('user_id', user.id)
          .eq('status', 'sent')
          .order('sent_at', { ascending: false })
          .limit(1)
          .single()

        if (lastReminder?.sent_at) {
          const lastReminderTime = new Date(lastReminder.sent_at)
          const timeSinceLastReminder = now.getTime() - lastReminderTime.getTime()
          const intervalMs = userPrefs.reminder_interval_minutes * 60 * 1000

          if (timeSinceLastReminder < intervalMs) {
            results.skipped++
            results.details.push({
              userId: user.id,
              status: 'skipped',
              reason: 'Too soon since last reminder'
            })
            continue
          }
        }

        // Send FCM notification
        if (firebaseServerKey && user.fcm_token) {
          const message: FCMMessage = {
            to: user.fcm_token,
            notification: {
              title: "💧 Hydration Reminder",
              body: "Time to drink some water! Stay hydrated and healthy.",
              icon: "/icons/icon-192x192.png"
            },
            data: {
              type: "hydration_reminder",
              userId: user.id,
              timestamp: now.toISOString()
            }
          }

          const fcmResponse = await fetch('https://fcm.googleapis.com/fcm/send', {
            method: 'POST',
            headers: {
              'Authorization': `key=${firebaseServerKey}`,
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(message)
          })

          if (fcmResponse.ok) {
            // Log successful reminder
            await supabase
              .from('reminders')
              .insert({
                user_id: user.id,
                scheduled_at: now.toISOString(),
                sent_at: now.toISOString(),
                status: 'sent',
                message: message.notification.body
              })

            results.sent++
            results.details.push({
              userId: user.id,
              status: 'sent'
            })
          } else {
            const errorText = await fcmResponse.text()
            console.error(`FCM error for user ${user.id}:`, errorText)
            
            // Log failed reminder
            await supabase
              .from('reminders')
              .insert({
                user_id: user.id,
                scheduled_at: now.toISOString(),
                status: 'failed',
                message: `FCM error: ${errorText}`
              })

            results.errors++
            results.details.push({
              userId: user.id,
              status: 'error',
              reason: `FCM error: ${fcmResponse.status}`
            })
          }
        } else {
          results.skipped++
          results.details.push({
            userId: user.id,
            status: 'skipped',
            reason: 'No FCM token or Firebase not configured'
          })
        }

      } catch (userError) {
        console.error(`Error processing user ${user.id}:`, userError)
        results.errors++
        results.details.push({
          userId: user.id,
          status: 'error',
          reason: userError instanceof Error ? userError.message : 'Unknown error'
        })
      }
    }

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Reminder job completed successfully',
        summary: results
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200 
      }
    )

  } catch (error) {
    console.error('Edge function error:', error)
    return new Response(
      JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500 
      }
    )
  }
})
