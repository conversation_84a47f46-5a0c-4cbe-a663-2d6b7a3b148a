import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface NotificationRequest {
  fcmToken: string
  title?: string
  body?: string
  data?: Record<string, string>
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Get Firebase Server Key from environment
    const firebaseServerKey = Deno.env.get('FIREBASE_SERVER_KEY')
    if (!firebaseServerKey) {
      throw new Error('Firebase Server Key not configured')
    }

    // Parse request body
    const { fcmToken, title, body, data }: NotificationRequest = await req.json()

    if (!fcmToken) {
      return new Response(
        JSON.stringify({ error: 'FCM token is required' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Prepare FCM payload
    const fcmPayload = {
      to: fcmToken,
      notification: {
        title: title || 'AquaBell Test Notification',
        body: body || 'This is a test notification from AquaBell! 💧',
        icon: '/icons/icon-192x192.png',
        badge: '/icons/badge-72x72.png',
        tag: 'test-notification',
        requireInteraction: false
      },
      data: {
        type: 'test_notification',
        timestamp: new Date().toISOString(),
        ...data
      },
      webpush: {
        headers: {
          Urgency: 'high'
        },
        notification: {
          title: title || 'AquaBell Test Notification',
          body: body || 'This is a test notification from AquaBell! 💧',
          icon: '/icons/icon-192x192.png',
          badge: '/icons/badge-72x72.png',
          tag: 'test-notification',
          requireInteraction: false,
          actions: [
            {
              action: 'drink',
              title: 'I drank water! 💧'
            },
            {
              action: 'snooze',
              title: 'Remind me later'
            }
          ]
        }
      }
    }

    // Send notification via FCM Legacy API
    const fcmResponse = await fetch('https://fcm.googleapis.com/fcm/send', {
      method: 'POST',
      headers: {
        'Authorization': `key=${firebaseServerKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(fcmPayload),
    })

    const fcmResult = await fcmResponse.json()

    if (!fcmResponse.ok) {
      console.error('FCM Error:', fcmResult)
      return new Response(
        JSON.stringify({ 
          error: 'Failed to send notification', 
          details: fcmResult 
        }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    console.log('Notification sent successfully:', fcmResult)

    return new Response(
      JSON.stringify({ 
        success: true, 
        messageId: fcmResult.message_id,
        fcmResponse: fcmResult
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('Error sending notification:', error)
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error', 
        message: error.message 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
