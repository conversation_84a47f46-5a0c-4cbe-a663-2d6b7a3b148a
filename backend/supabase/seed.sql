-- AquaBell Seed Data
-- This file contains test data for local development

-- Note: This seed data will only work after users are created through authentication
-- The trigger will automatically create user profiles and preferences

-- For now, we'll just add some helpful notices
-- Test data will be created automatically when users register through the app

DO $$
BEGIN
    RAISE NOTICE 'AquaBell database schema has been initialized successfully!';
    RAISE NOTICE 'Test data will be available after user registration through the app.';
    RAISE NOTICE 'The handle_new_user() trigger will automatically create user profiles and preferences.';
END $$;

-- Insert some helpful comments for development
COMMENT ON TABLE public.users IS 'User profiles extending Supabase auth.users';
COMMENT ON TABLE public.user_preferences IS 'User hydration preferences and settings';
COMMENT ON TABLE public.hydration_logs IS 'Individual water intake logs';
COMMENT ON TABLE public.reminders IS 'Scheduled hydration reminders';

COMMENT ON COLUMN public.users.fcm_token IS 'Firebase Cloud Messaging token for push notifications';
COMMENT ON COLUMN public.users.timezone IS 'User timezone for scheduling reminders';
COMMENT ON COLUMN public.user_preferences.daily_goal_ml IS 'Daily hydration goal in milliliters';
COMMENT ON COLUMN public.user_preferences.wake_time IS 'Time when user typically wakes up';
COMMENT ON COLUMN public.user_preferences.sleep_time IS 'Time when user typically goes to sleep';
COMMENT ON COLUMN public.user_preferences.reminder_interval_minutes IS 'Minutes between hydration reminders';
