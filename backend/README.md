# AquaBell Backend Testing

This directory contains backend utilities for testing AquaBell functionality.

## FCM Notification Testing

### test-fcm.js

A Node.js script for testing Firebase Cloud Messaging notifications from the backend.

#### Usage

```bash
node test-fcm.js <FCM_TOKEN>
```

#### Setup

1. **Get Firebase Server Key:**
   - Go to Firebase Console → Project Settings → Cloud Messaging
   - Copy the Server Key

2. **Set Environment Variable:**
   ```bash
   export FIREBASE_SERVER_KEY="your_firebase_server_key_here"
   ```

3. **Get FCM Token:**
   - Open AquaBell app at http://localhost:3000
   - Navigate to Test Notifications page
   - Copy the FCM token displayed

4. **Run Test:**
   ```bash
   node test-fcm.js "your_fcm_token_here"
   ```

#### Example Output

```
🧪 AquaBell FCM Testing Script
================================
🚀 Sending FCM notification...
📱 Target token: eGxVdJHxTkuY1234567890...
✅ Notification sent successfully!
📊 Response: {
  "multicast_id": 1234567890123456789,
  "success": 1,
  "failure": 0,
  "canonical_ids": 0,
  "results": [
    {
      "message_id": "0:1234567890123456%abcd1234f9fd7ecd"
    }
  ]
}
🎉 Notification delivered successfully
📧 Message ID: 0:1234567890123456%abcd1234f9fd7ecd
```

#### Troubleshooting

- **Invalid FCM token**: Make sure you copied the complete token from the test page
- **Authentication error**: Verify your Firebase Server Key is correct
- **Network error**: Check your internet connection and Firebase project settings

## Supabase Edge Functions

### send-test-notification

A Supabase Edge Function for sending FCM notifications from the cloud.

#### Deployment

```bash
supabase functions deploy send-test-notification
```

#### Usage

```bash
curl -X POST 'https://your-project.supabase.co/functions/v1/send-test-notification' \
  -H 'Authorization: Bearer YOUR_ANON_KEY' \
  -H 'Content-Type: application/json' \
  -d '{
    "fcmToken": "your_fcm_token",
    "title": "Test Notification",
    "body": "This is a test from Supabase!"
  }'
```

## Notes

- The FCM testing script uses the legacy FCM API for simplicity
- For production applications, consider migrating to the HTTP v1 API
- Always keep your Firebase Server Key secure and never expose it in client-side code